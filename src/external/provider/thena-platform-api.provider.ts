import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { FileShareMessageEvent } from '@slack/types';
import { WebClient } from '@slack/web-api';
import { ConfigKeys, ConfigService } from '../../config/config.service';
import { Installations, Organizations } from '../../database/entities';
import { Ticket } from '../../platform/interfaces';
import { CUSTOM_LOGGER_TOKEN, ILogger, safeJsonStringify } from '../../utils';
import { UserPlatformLookupService } from '../../shared/user-platform-lookup/user-platform-lookup.service';
import {
  PlatformApiUnavailableError,
  PlatformApiTimeoutError,
  PlatformApiConnectionError
} from './errors/platform-api.errors';
import {
  Account,
  CreateAccount,
  CreateCustomField,
  CreateNewComment,
  CreateNewTicket,
  CustomField,
  SearchCustomField,
  UpdateAccount,
  UpdateTicketData,
} from './interfaces';
import { UpdateComment } from './interfaces/comments.interface';
import {
  AddCustomObjectField,
  CreateCustomObject,
  CreateCustomObjectRecord,
  CustomObjectField,
  CustomObjectRecord,
  SearchCustomObject,
  UpdateCustomObjectRecord,
} from './interfaces/custom-objects.interface';
import { CustomObject } from './interfaces/custom-objects.interface';
import {
  CreateCustomerContactDTO,
  IngestCustomerContactDTO,
  PlatformCustomerContact,
  UpdateCustomerContactDTO,
} from './interfaces/customer-contacts.interface';

// @ts-ignore
import Link from '@tiptap/extension-link';
// @ts-ignore
import Mention from '@tiptap/extension-mention';
// @ts-ignore
import Placeholder from '@tiptap/extension-placeholder';
// @ts-ignore
import { generateJSON } from '@tiptap/html';
// @ts-ignore
import StarterKit from '@tiptap/starter-kit';

const LOG_SPAN = '[THENA_PLATFORM_API_PROVIDER]';

interface LinkUsersToPlatformPayload {
  externalType: 'slack';
  details: {
    email: string;
    slackSinkDetails: {
      id: string;
      teamId: string;
    };
  }[];
}

@Injectable()
export class ThenaPlatformApiProvider {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly configService: ConfigService,
    private readonly userPlatformLookupService: UserPlatformLookupService,
  ) {}

  /**
   * Check if the platform API is available
   * @returns Promise<boolean> indicating if the platform API is reachable
   */
  async isApiAvailable(): Promise<boolean> {
    try {
      const baseUrl = this.configService.get(ConfigKeys.PLATFORM_API_URL);
      const url = `${baseUrl.replace(/\/+$/, '')}/health`;

      const response = await fetch(url, {
        method: 'GET',
        signal: AbortSignal.timeout(5000), // 5 second timeout for health check
      });

      return response.ok;
    } catch (error) {
      this.logger.warn(
        `${LOG_SPAN} Platform API health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      return false;
    }
  }

  /**
   * Get API key
   * @param organization Organization
   * @returns API key
   */
  private getApiKey(organization: Organizations) {
    return organization.apiKey;
  }

  /**
   * Validate API key and throw error if missing
   * @param organization Organization
   * @throws Error if API key is missing
   */
  private validateApiKey(organization: Organizations): void {
    const apiKey = this.getApiKey(organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }
  }

  /**
   * Handle API response errors with consistent error handling
   * @param response Response object
   * @param operation Operation name for logging
   * @param installation Installation context for auth error handling
   * @throws Error with appropriate message
   */
  private async handleApiError(
    response: Response,
    operation: string,
    installation?: Installations,
  ): Promise<void> {
    if (response.ok) {
      return;
    }

    let responseJson: any;
    try {
      responseJson = await response.json();
    } catch {
      responseJson = { message: await response.text?.() };
    }

    // Handle authentication errors specifically
    if (response.status === 401 && installation) {
      this.logger.error(
        `${LOG_SPAN} Authentication failed during ${operation} - API key does not belong to a privileged app. Status: ${response.status}, API Key: ${this.getApiKey(installation.organization)?.substring(0, 10)}..., Org ID: ${installation.organization.uid}`,
      );
      throw new Error('Authentication failed: API key does not belong to a privileged app. Please check your API key configuration.');
    }

    this.logger.error(`${LOG_SPAN} Failed to ${operation}!`, responseJson);
    throw new Error(responseJson.message || `Failed to ${operation}`);
  }

  /**
   * Create X-User-ID header for platform user impersonation
   * @param slackUserId Slack user ID
   * @param installation Installation context
   * @returns Custom headers object with X-User-ID if available
   */
  private async createXUserIdHeaders(
    slackUserId: string | undefined,
    installation: Installations,
  ): Promise<Record<string, string>> {
    if (!slackUserId) {
      return {};
    }

    try {
      const platformUserId = await this.userPlatformLookupService.getPlatformUserId(
        slackUserId,
        installation,
      );

      if (platformUserId) {
        this.logger.log(
          `${LOG_SPAN} Adding X-User-ID header for Slack user ${slackUserId}: ${platformUserId}`,
        );
        return { 'X-User-ID': platformUserId };
      }

      this.logger.debug(
        `${LOG_SPAN} No platform user ID found for Slack user ${slackUserId}`,
      );
      return {};
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `${LOG_SPAN} Failed to lookup platform user ID for Slack user ${slackUserId}: ${errorMessage}`,
      );
      return {};
    }
  }

  /**
   * Proxy request through Annotator API
   * @param organization Organization
   * @param method Method
   * @param path Path
   * @param body Body
   * @returns Response
   */
  private proxyAnnotator<T>(
    organization: Organizations,
    method: string,
    path: string,
    body?: T extends object ? T : never,
  ): Promise<Response> {
    // Get API key from organization
    const apiKey = this.getApiKey(organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Construct the base URL
    const baseUrl = this.configService.get(ConfigKeys.ANNOTATOR_API_URL);

    // Construct the payload
    const payload: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-org-id': organization.uid,
        'x-api-key': apiKey,
      },
    };

    // Add body if it exists
    if (body) {
      payload.body = JSON.stringify(body);
    } else if (method !== 'GET') {
      payload.body = JSON.stringify({});
    }

    // Ensure proper URL construction by removing trailing/leading slashes
    const cleanBaseUrl = baseUrl.replace(/\/+$/, '');
    const cleanPath = path.replace(/^\/+/, '');
    const url = `${cleanBaseUrl}/${cleanPath}`;

    // Generate the request
    return fetch(url, payload);
  }

  /**
   * Proxy request
   * @param organization Organization
   * @param method Method
   * @param path Path
   * @param body Body
   * @returns Response
   */
  proxy<T>(
    organization: Organizations,
    method: string,
    path: string,
    body?: T extends object ? T : never,
  ): Promise<Response>;

  /**
   * Proxy request with custom headers
   * @param organization Organization
   * @param method Method
   * @param path Path
   * @param body Body
   * @param customHeaders Custom headers to include
   * @returns Response
   */
  proxy<T>(
    organization: Organizations,
    method: string,
    path: string,
    body?: T extends object ? T : never,
    customHeaders?: Record<string, string>,
  ): Promise<Response>;

  proxy<T>(
    organization: Organizations,
    method: string,
    path: string,
    body?: T extends object ? T : never,
    customHeaders?: Record<string, string>,
  ): Promise<Response> {
    // Get API key from organization
    const apiKey = this.getApiKey(organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Construct the base URL
    const baseUrl = this.configService.get(ConfigKeys.PLATFORM_API_URL);

    // Construct the default headers
    const defaultHeaders = {
      'Content-Type': 'application/json',
      'x-org-id': organization.uid,
      'x-api-key': apiKey,
      'x-request-source': 'slack',
    };

    // Merge custom headers with default headers
    const headers = customHeaders ? { ...defaultHeaders, ...customHeaders } : defaultHeaders;

    // Construct the payload
    const payload: RequestInit = {
      method,
      headers,
    };

    // Add body if it exists
    if (body) {
      payload.body = JSON.stringify(body);
    } else if (method !== 'GET') {
      payload.body = JSON.stringify({});
    }

    // Ensure proper URL construction by removing trailing/leading slashes
    const cleanBaseUrl = baseUrl.replace(/\/+$/, '');
    const cleanPath = path.replace(/^\/+/, '');
    const url = `${cleanBaseUrl}/${cleanPath}`;

    this.logger.log(`${LOG_SPAN} Making ${method} request to ${url}`);

    // Generate the request with timeout
    return fetch(url, {
      ...payload,
      signal: AbortSignal.timeout(10000), // 10 second timeout
    }).catch((error) => {
      // Safely stringify the error with a fallback to prevent circular reference issues
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown network error';
      const errorDetails = safeJsonStringify(error, {
        fallback: `Network error: ${errorMessage}`,
        handleCircular: true,
        maxDepth: 5,
      });

      // Check if this is a connection refused error (service not running)
      if (errorMessage.includes('ECONNREFUSED') || errorMessage.includes('fetch failed')) {
        this.logger.error(
          `${LOG_SPAN} Platform API service is not available at ${url}. Please ensure the platform API is running on ${baseUrl}`,
        );
        throw new PlatformApiUnavailableError(`Platform API service unavailable. Please check if the service is running at ${baseUrl}`, `${method} ${path}`);
      }

      // Check if this is a timeout error
      if (errorMessage.includes('timeout') || errorMessage.includes('AbortError')) {
        this.logger.error(
          `${LOG_SPAN} Request timeout for ${method} ${url}. The platform API may be overloaded or slow to respond.`,
        );
        throw new PlatformApiTimeoutError('Platform API request timeout. The service may be overloaded.', `${method} ${path}`);
      }

      this.logger.error(
        `${LOG_SPAN} Network error for ${method} ${url}: ${errorDetails}`,
      );
      throw new PlatformApiConnectionError(`Network error: ${errorMessage}`, `${method} ${path}`);
    });
  }

  /**
   * Create new ticket
   * @param installation Installation
   * @param data Create new ticket data
   */
  async createNewTicket(installation: Installations, data: CreateNewTicket) {
    this.validateApiKey(installation.organization);

    this.logger.log(
      `${LOG_SPAN} Creating ticket in platform with data: ${JSON.stringify(data)}`,
    );

    // Raw ticket response
    const ticketResponse = await this.proxy(
      installation.organization,
      'POST',
      '/v1/tickets',
      {
        requestorEmail: data.requestorEmail,
        title: data.title,
        description: data.description,
        teamId: data.teamId,
        performRouting: !!data.subTeamId,
        priorityId: data.urgency,
        metadata: {
          slackTeamId: installation.teamId,
          slack: {
            channel: data.metadata.slack.channel,
            ts: data.metadata.slack.ts,
            user: data.metadata.slack.user,
          },
        },
      },
    );

    await this.handleApiError(ticketResponse, 'create ticket', installation);

    // Parse the ticket response
    const ticket = await ticketResponse.json();
    return ticket.data as Ticket;
  }

  /**
   * Link users to platform
   * @param installation Installation
   * @param payload Link users to platform payload
   * @returns Linked users
   */
  async linkUsersToPlatform(
    installation: Installations,
    payload: LinkUsersToPlatformPayload,
  ) {
    this.validateApiKey(installation.organization);

    // Proxy the request
    const linkUsersToPlatformResponse = await this.proxy(
      installation.organization,
      'PATCH',
      '/v1/users/ingest/link-external',
      payload,
    );

    await this.handleApiError(linkUsersToPlatformResponse, 'link users to platform', installation);

    // Parse the response
    const linkUsersToPlatform = await linkUsersToPlatformResponse.json();
    return linkUsersToPlatform.data;
  }

  /**
   * Get entity details
   * @param installation Installation
   * @param identifier Identifier
   * @param entityId Entity ID
   * @param entityType Entity type
   */
  async getEntityDetails(
    installation: Installations,
    identifier: string,
    entityId: string,
    entityType: string,
  ) {
    this.validateApiKey(installation.organization);

    // Get entity details
    const entityResponse = await this.proxyAnnotator(
      installation.organization,
      'POST',
      '/v1/annotator/data',
      {
        entityType,
        data: { [identifier]: entityId },
        relations: [
          'account',
          'team',
          'subTeam',
          'priority',
          'sentiment',
          'status',
          'type',
          'customFieldValues',
          'assignedAgent',
          'customerContact',
        ],
      },
    );

    // If the request failed, throw an error
    const entity = await entityResponse.json();
    if (!entityResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to get entity details!`);
      throw new Error(entity.message);
    }

    return entity.data;
  }

  private async uploadToPlatform(
    installation: Installations,
    files: FileShareMessageEvent['files'],
  ) {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Create the slack web api client
    const slackClient = new WebClient(installation.botToken, {
      rejectRateLimitedCalls: true,
    });

    // Upload the files to the platform
    const uploadPromises = files
      .map(async (file) => {
        let fileInfo = file;

        // If the file is external we need to pull data
        // @see https://api.slack.com/events/message/file_share
        if ((file as any).file_access === 'check_file_info') {
          const fileInfoResponse = await slackClient.files.info({
            file: file.id,
          });

          if (fileInfoResponse.ok) {
            fileInfo = fileInfoResponse.file as any;
          }
        }

        // Get the file url
        const fileUrl = fileInfo.url_private_download;
        if (!fileUrl) {
          return null;
        }

        // Get the file data
        const fileDataResponse = await fetch(fileUrl, {
          headers: {
            Authorization: `Bearer ${installation.botToken}`,
          },
        });

        // If the file data was not fetched, return null
        if (!fileDataResponse.ok) {
          return null;
        }

        // Get the file blob
        const fileBlob = await fileDataResponse.blob();

        // Determine the correct MIME type based on file extension
        const mimeType = fileInfo.mimetype;

        // Create form data with proper MIME type
        const formData = new FormData();
        const fileName = fileInfo.name || 'slack_file';
        formData.append(
          'files',
          new File([fileBlob], fileName, { type: mimeType }),
          fileName,
        );

        const baseUrl = this.configService.get(ConfigKeys.PLATFORM_API_URL);
        const payload: RequestInit = {
          method: 'POST',
          headers: {
            'x-org-id': installation.organization.uid,
            'x-api-key': apiKey,
            'x-request-source': 'slack',
            accept: '*/*',
          },
          body: formData,
        };

        const cleanBaseUrl = baseUrl.replace(/\/+$/, '');
        const cleanPath = 'storage/upload-file';
        const url = `${cleanBaseUrl}/${cleanPath}`;

        const uploadResponse = await fetch(url, payload);

        const uploadResponseJson = await uploadResponse.json();
        return uploadResponseJson.data;
      })
      .filter(Boolean);

    const result = await Promise.all(uploadPromises);
    return result;
  }

  /**
   * Create new comment
   * @param installation Installation
   * @param data Create new comment data
   */
  async createNewComment(installation: Installations, data: CreateNewComment) {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Construct the slack thread link
    let slackThreadLink = `slack://channel?team=${installation.teamId}&id=${data.channelId}`;
    if (data.metadata.threadTs && data.metadata.ts) {
      slackThreadLink += `&message=${data.metadata.ts}&thread_ts=${data.metadata.threadTs}`;
    } else if (data.metadata.ts) {
      slackThreadLink += `&message=${data.metadata.ts}&thread_ts=${data.metadata.ts}`;
    }

    // Convert the html content to json
    const contentJson = generateJSON(data.content ?? '', [
      StarterKit.configure({
        heading: false,
      }),
      Link,
      Mention,
      Placeholder,
    ]);

    // Get the files from the event and upload them to the platform
    const files = data.files;
    const uploadedFiles = await this.uploadToPlatform(installation, files);

    const attachmentIds = uploadedFiles.map((data) => data?.data?.uid);

    // Construct the payload
    const payload = {
      content: data.content,
      contentHtml: data.htmlContent ?? data.content,
      attachmentIds,
      contentJson: JSON.stringify(contentJson),
      parentCommentId: data.parentCommentId,
      commentVisibility: data.commentVisibility,
      metadata: {
        external_sinks: {
          slack: {
            ignoreSelf: data.metadata.ignoreSelf,
            threadTs: data.metadata?.threadTs,
            ts: data.metadata.ts,
            slackThreadLink,
          },
        },
      },
      impersonatedUserEmail: data.impersonatedUserEmail,
      impersonatedUserName: data.impersonatedUserName,
      impersonatedUserAvatar: data.impersonatedUserAvatar,
    };

    // Create X-User-ID headers if Slack user ID is provided
    const customHeaders = await this.createXUserIdHeaders(data.metadata.slackUserId, installation);

    // Raw comment response
    const commentResponse = await this.proxy(
      installation.organization,
      'POST',
      `/v1/tickets/${data.ticketId}/comment`,
      payload,
      customHeaders,
    );

    await this.handleApiError(commentResponse, 'create comment', installation);

    // Parse the comment response
    const comment = await commentResponse.json();
    return comment as { data: { id: string } };
  }

  /**
   * Set slack auth
   * @param installation Installation
   * @param userEmail User email
   * @returns Set slack auth response
   */
  async setSlackAuth(
    installation: Installations,
    userEmail: string,
    unset?: boolean,
  ) {
    this.validateApiKey(installation.organization);

    // Proxy the request
    const encodedEmail = userEmail.replace(' ', '+');

    // Construct the url
    let url = `/v1/users/metadata/slack-auth?userEmail=${encodedEmail}&teamId=${installation.teamId}`;
    if (unset) {
      url = `/v1/users/metadata/slack-auth?userEmail=${encodedEmail}&teamId=${installation.teamId}&unset=true`;
    }

    // Set slack auth
    const setSlackAuthResponse = await this.proxy(
      installation.organization,
      'PATCH',
      url,
    );

    await this.handleApiError(setSlackAuthResponse, 'set slack auth', installation);

    return setSlackAuthResponse.json();
  }

  /**
   * Fetch forms for team
   *
   * NOTE: The API is paginated means you'll have a `results` array
   * @param installation Installation
   * @param teamId Team ID
   * @returns Forms
   */
  async fetchFormsForTeam(installation: Installations, teamId: string) {
    this.validateApiKey(installation.organization);

    // Get forms from platform
    const formsResponse = await this.proxy(
      installation.organization,
      'GET',
      `/v1/forms?onlyTeamForms=true&teamId=${teamId}`,
    );

    await this.handleApiError(formsResponse, 'fetch forms for team', installation);

    // Parse the response
    const forms = await formsResponse.json();
    return forms.data;
  }

  /**
   * Update comment
   * @param installation Installation
   * @param data Update comment data
   */
  async updateComment(installation: Installations, data: UpdateComment) {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Construct the payload
    const payload = {
      content: data.content,
      contentHtml: data.htmlContent,
    };

    // Raw comment response
    const commentResponse = await this.proxy(
      installation.organization,
      'PATCH',
      `/v1/comments/${data.commentId}`,
      payload,
    );

    // If the comment was not created, throw an error
    if (!commentResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to update comment in platform!`);
      const commentJson = await commentResponse.json();
      throw new Error(commentJson.message);
    }

    // Parse the comment response
    const comment = await commentResponse.json();
    return comment as { data: { id: string } };
  }

  /**
   * Update comment with metadata
   * @param installation Installation
   * @param data Update comment data with metadata
   */
  async updateCommentWithMetadata(
    installation: Installations,
    commentId: string,
    metadata: {
      external_sinks: {
        slack: {
          ignoreSelf: boolean;
          threadTs: string;
          ts: string;
          slackThreadLink: string;
        };
      };
    }
  ) {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Construct the payload with metadata
    const payload = {
      metadata
    };

    // Raw comment response
    const commentResponse = await this.proxy(
      installation.organization,
      'PATCH',
      `/v1/comments/${commentId}`,
      payload,
    );

    // If the comment metadata was not updated, throw an error
    if (!commentResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to update comment metadata in platform!`);
      const commentJson = await commentResponse.json();
      throw new Error(commentJson.message);
    }

    // Parse the comment response
    const comment = await commentResponse.json();
    return comment as { data: { id: string } };
  }

  /**
   * Delete comment
   * @param installation Installation
   * @param commentId Comment ID
   */
  async deleteComment(installation: Installations, commentId: string) {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Get organization
    const organization = installation.organization;

    // Proxy the request
    const deleteCommentResponse = await this.proxy(
      organization,
      'DELETE',
      `/v1/comments/${commentId}`,
    );

    // If the request failed, throw an error
    if (!deleteCommentResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to delete comment in platform!`);
      const deleteCommentJson = await deleteCommentResponse.json();
      throw new Error(deleteCommentJson.message);
    }

    // Parse the response
    return { ok: true };
  }

  /**
   * Get teams
   * @param installation Installation
   * @returns Teams
   */
  async getTeams(installation: Installations) {
    // Get API key from organization
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Get organization
    const organization = installation.organization;

    try {
      this.logger.log(
        `${LOG_SPAN} Fetching teams for organization ${organization.id}`,
      );

      // Fetch teams from platform
      const getTeamsResponse = await this.proxy<any>(
        organization,
        'GET',
        'v1/teams',
      );

      if (!getTeamsResponse.ok) {
        const errorText = await getTeamsResponse.text();
        this.logger.error(
          `${LOG_SPAN} API returned error status ${getTeamsResponse.status}: ${errorText}`,
        );
        throw new Error(`Teams API returned status ${getTeamsResponse.status}`);
      }

      const responseJson = await getTeamsResponse.json();

      if (!responseJson.data || !Array.isArray(responseJson.data)) {
        this.logger.error(
          `${LOG_SPAN} Unexpected API response format: ${safeJsonStringify(responseJson, { fallback: 'Invalid response format' })}`,
        );
        throw new Error('Teams API returned unexpected data format');
      }

      this.logger.log(
        `${LOG_SPAN} Successfully fetched ${responseJson.data.length} teams`,
      );
      const teams = responseJson.data.map((t: any) => ({ id: t.id, name: t.name }));

      return teams;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} Error fetching teams: ${safeJsonStringify(error, { fallback: 'Unknown error fetching teams' })}`,
      );
      throw new Error(
        `Failed to fetch teams: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get statuses for team
   * @param installation Installation
   * @param teamId Team ID
   * @returns Statuses
   */
  async getStatusesForTeam(installation: Installations, teamId: string) {
    // Get API key from organization
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const getStatusesResponse = await this.proxy(
      installation.organization,
      'GET',
      `/v1/tickets/status?teamId=${teamId}`,
    );

    // If the request failed, throw an error
    if (!getStatusesResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to get statuses for team!`);
      const statusesJson = await getStatusesResponse.json();
      throw new Error(statusesJson.message);
    }

    // Parse the response
    const statuses = await getStatusesResponse.json();
    return statuses.data;
  }

  /**
   * Get priorities for team
   * @param installation Installation
   * @param teamId Team ID
   * @returns Priorities
   */
  async getPrioritiesForTeam(installation: Installations, teamId: string) {
    // Get API key from organization
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const getPrioritiesResponse = await this.proxy(
      installation.organization,
      'GET',
      `/v1/tickets/priority?teamId=${teamId}`,
    );

    // If the request failed, throw an error
    if (!getPrioritiesResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to get priorities for team!`);
      const prioritiesJson = await getPrioritiesResponse.json();
      throw new Error(prioritiesJson.message);
    }

    // Parse the response
    const priorities = await getPrioritiesResponse.json();
    return priorities.data;
  }

  /**
   * Update ticket
   * @param installation Installation
   * @param ticketId Ticket ID
   * @param data Update ticket data
   * @returns Updated ticket
   */
  async updateTicket(
    installation: Installations,
    ticketId: string,
    data: Partial<UpdateTicketData>,
  ) {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const updateTicketResponse = await this.proxy(
      installation.organization,
      'PATCH',
      `/v1/tickets/${ticketId}`,
      data,
    );

    // If the request failed, throw an error
    if (!updateTicketResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to update ticket!`);
      const updateTicketJson = await updateTicketResponse.json();
      throw new Error(updateTicketJson.message);
    }

    // Parse the response
    const ticket = await updateTicketResponse.json();
    return ticket as { data: { id: string } };
  }

  /**
   * Get ticket
   * @param installation Installation
   * @param ticketId Ticket ID
   * @returns Ticket
   */
  async getTicket(installation: Installations, ticketId: string) {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const getTicketResponse = await this.proxy(
      installation.organization,
      'GET',
      `/v1/tickets/${ticketId}`,
    );

    if (!getTicketResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to get ticket!`);
      const getTicketJson = await getTicketResponse.json();
      throw new Error(getTicketJson.message);
    }

    // Parse the response and return the ticket data
    const ticket = await getTicketResponse.json();
    return ticket.data;
  }

  /**
   * Get team members
   * @param installation Installation
   * @param teamId Team ID
   * @param searchQuery Search query
   * @returns Team members
   */
  async getTeamMembers(
    installation: Installations,
    teamId: string,
    searchQuery?: string,
  ) {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    const apiUrl = searchQuery
      ? `/v1/teams/${teamId}/members?searchQuery=${searchQuery}`
      : `/v1/teams/${teamId}/members`;
    // Proxy the request
    const getTeamMembersResponse = await this.proxy(
      installation.organization,
      'GET',
      apiUrl,
    );

    if (!getTeamMembersResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to get team members!`);
      const getTeamMembersJson = await getTeamMembersResponse.json();
      throw new Error(getTeamMembersJson.message);
    }

    // Parse the response
    const teamMembers = await getTeamMembersResponse.json();
    return teamMembers.data;
  }

  /**
   * Close ticket
   * @param installation Installation
   * @param ticketId Ticket ID
   * @param ticketTeamId Ticket Team ID
   * @returns Closed ticket
   */
  async closeTicket(
    installation: Installations,
    ticketId: string,
    ticketTeamId: string,
  ) {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Get all the status
    const statuses = await this.getStatusesForTeam(installation, ticketTeamId);

    // Get parent closed status
    const closedStatus = statuses.find(
      (s: any) => s.name === 'Closed' && !s.parentStatusId,
    );

    // Update the ticket status
    await this.updateTicket(installation, ticketId, {
      statusId: closedStatus.id,
    });
  }

  /**
   * Archive ticket
   * @param installation Installation
   * @param ticketId Ticket ID
   * @returns Archived ticket
   */
  async archiveTicket(installation: Installations, ticketId: string) {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const archiveTicketResponse = await this.proxy(
      installation.organization,
      'PATCH',
      `/v1/tickets/${ticketId}/archive`,
    );

    if (!archiveTicketResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to archive ticket!`);
      const archiveTicketJson = await archiveTicketResponse.json();
      throw new Error(archiveTicketJson.message);
    }

    // Parse the response
    const ticket = await archiveTicketResponse.json();
    return ticket as { data: { id: string } };
  }

  /**
   * Create custom field
   * @param installation Installation
   * @param data {@link CreateCustomField} Create custom field data
   * @returns Created custom field {@link CustomField}
   */
  async createCustomField(
    installation: Installations,
    data: CreateCustomField,
  ): Promise<CustomField> {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const createCustomFieldResponse = await this.proxy(
      installation.organization,
      'POST',
      '/v1/custom-field',
      data,
    );

    if (!createCustomFieldResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to create custom field!`);
      const createCustomFieldJson = await createCustomFieldResponse.json();
      throw new Error(createCustomFieldJson.message);
    }

    // Parse the response
    const customField = await createCustomFieldResponse.json();
    return customField.data;
  }

  /**
   * Search custom field
   * @param installation Installation
   * @param data {@link SearchCustomField} Search custom field data
   * @returns Searched custom field {@link CustomField}
   */
  async searchCustomField(
    installation: Installations,
    data: SearchCustomField,
  ): Promise<CustomField[]> {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    let searchParams = `name=${data.name}`;
    if (data.teamId) {
      searchParams += `&teamId=${data.teamId}`;
    }

    // Proxy the request
    const searchCustomFieldResponse = await this.proxy(
      installation.organization,
      'GET',
      `v1/custom-field/search?${searchParams}`,
    );

    if (!searchCustomFieldResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to search custom field!`);
      const searchCustomFieldJson = await searchCustomFieldResponse.json();
      throw new Error(searchCustomFieldJson.message);
    }

    // Parse the response
    const customField = await searchCustomFieldResponse.json();
    return customField.data;
  }

  /**
   * Filter accounts by domains
   * @param installation Installation
   * @param domains Domains
   * @returns Accounts {@link Account[]}
   */
  async getAccountsByDomains(
    installation: Installations,
    domains: string[],
  ): Promise<Account[]> {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const getAccountsByDomainsResponse = await this.proxy(
      installation.organization,
      'POST',
      '/v1/accounts/filter/primary-domains',
      {
        primaryDomains: domains,
      },
    );

    if (!getAccountsByDomainsResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to get accounts by domains!`);
      const getAccountsByDomainsJson =
        await getAccountsByDomainsResponse.json();
      throw new Error(getAccountsByDomainsJson.message);
    }

    // Parse the response
    const accounts = await getAccountsByDomainsResponse.json();
    return accounts.data;
  }

  /**
   * Filter accounts by IDs
   * @param installation Installation
   * @param ids IDs
   * @returns Accounts {@link Account[]}
   */
  async getAccountsByIds(
    installation: Installations,
    ids: string[],
  ): Promise<Account[]> {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const getAccountsByIdsResponse = await this.proxy(
      installation.organization,
      'POST',
      '/v1/accounts/filter/ids',
      { ids },
    );

    if (!getAccountsByIdsResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to get accounts by IDs!`);
      const getAccountsByIdsJson = await getAccountsByIdsResponse.json();
      throw new Error(getAccountsByIdsJson.message);
    }

    // Parse the response
    const accounts = await getAccountsByIdsResponse.json();
    return accounts.data;
  }

  /**
   * Get comment threads
   * @param installation Installation
   * @param commentId Comment ID
   * @returns Comment threads
   */
  async getCommentThreads(installation: Installations, commentId: string) {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const getCommentThreadsResponse = await this.proxy(
      installation.organization,
      'GET',
      `/v1/comments/${commentId}/threads`,
    );

    // If the request failed, throw an error
    if (!getCommentThreadsResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to get comment threads!`);
      const getCommentThreadsJson = await getCommentThreadsResponse.json();

      // If the comment was not found, return an empty array
      if (getCommentThreadsJson.statusCode === HttpStatus.NOT_FOUND) {
        return [];
      }

      throw new Error(getCommentThreadsJson.message);
    }

    // Parse the response
    const threads = await getCommentThreadsResponse.json();
    return threads.data;
  }

  /**
   * Create account
   * @param installation Installation
   * @param data {@link CreateAccount} Create account data
   * @returns Created account {@link Account}
   */
  async createAccount(
    installation: Installations,
    data: CreateAccount,
  ): Promise<Account> {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const createAccountResponse = await this.proxy(
      installation.organization,
      'POST',
      '/v1/accounts',
      data,
    );

    if (!createAccountResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to create account!`);
      const createAccountJson = await createAccountResponse.json();
      throw new Error(createAccountJson.message);
    }

    // Parse the response
    const account = await createAccountResponse.json();
    return account.data;
  }

  /**
   * Create accounts
   * @param installation Installation
   * @param data {@link CreateAccount[]} Create accounts data
   * @returns Created accounts {@link Account[]}
   */
  async createAccounts(
    installation: Installations,
    data: CreateAccount[],
  ): Promise<Account[]> {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const createAccountsResponse = await this.proxy(
      installation.organization,
      'POST',
      '/v1/accounts/bulk',
      data,
    );

    if (!createAccountsResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to create accounts!`);
      const createAccountsJson = await createAccountsResponse.json();
      throw new Error(createAccountsJson.message);
    }

    // Parse the response
    const accounts = await createAccountsResponse.json();
    return accounts.data;
  }

  /**
   * Update account
   * @param installation Installation
   * @param accountId Account ID
   * @param data {@link UpdateAccount} Update account data
   * @returns Updated account {@link Account}
   */
  async updateAccount(
    installation: Installations,
    accountId: string,
    data: UpdateAccount,
  ): Promise<Account> {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const updateAccountResponse = await this.proxy(
      installation.organization,
      'PUT',
      `/v1/accounts/${accountId}`,
      data,
    );

    if (!updateAccountResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to update account!`);
      const updateAccountJson = await updateAccountResponse.json();
      throw new Error(updateAccountJson.message);
    }

    // Parse the response
    const account = await updateAccountResponse.json();
    return account.data;
  }

  /**
   * Create custom object
   * @param installation Installation
   * @param data {@link CreateCustomObject} Create custom object data
   * @returns Created custom object {@link CustomObject}
   */
  async createCustomObject(
    installation: Installations,
    data: CreateCustomObject,
  ): Promise<CustomObject> {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const createCustomObjectResponse = await this.proxy(
      installation.organization,
      'POST',
      '/v1/custom-object',
      data,
    );

    if (!createCustomObjectResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to create custom object!`);
      const createCustomObjectJson = await createCustomObjectResponse.json();
      throw new Error(createCustomObjectJson.message);
    }

    // Parse the response
    const customObject = await createCustomObjectResponse.json();
    return customObject;
  }

  /**
   * Search custom object
   * @param installation Installation
   * @param data {@link SearchCustomObject} Search custom object data
   * @returns Searched custom object {@link CustomObject}
   */
  async searchCustomObject(
    installation: Installations,
    data: SearchCustomObject,
  ): Promise<CustomObject[]> {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const searchCustomObjectResponse = await this.proxy(
      installation.organization,
      'GET',
      `/v1/custom-object/search?term=${data.name}`,
    );

    if (!searchCustomObjectResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to search custom object!`);
      const searchCustomObjectJson = await searchCustomObjectResponse.json();
      throw new Error(searchCustomObjectJson.message);
    }

    // Parse the response
    const customObject = await searchCustomObjectResponse.json();
    return customObject.data;
  }

  /**
   * Add a custom field to a custom object
   * @param installation Installation
   * @param data {@link AddCustomObjectField} Add custom object field data
   * @returns Added custom object field {@link CustomObjectField}
   */
  async addCustomObjectField(
    installation: Installations,
    data: AddCustomObjectField,
  ): Promise<CustomObjectField> {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    const { customObjectId, customFieldId, ...rest } = data;

    // Proxy the request
    const addCustomObjectFieldResponse = await this.proxy(
      installation.organization,
      'POST',
      `/v1/custom-object/${customObjectId}/fields/${customFieldId}`,
      rest,
    );

    if (!addCustomObjectFieldResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to add custom object field!`);
      const addCustomObjectFieldJson =
        await addCustomObjectFieldResponse.json();
      throw new Error(addCustomObjectFieldJson.message);
    }

    // Parse the response
    const customObjectField = await addCustomObjectFieldResponse.json();
    return customObjectField.data;
  }

  /**
   * Create a custom object record
   * @param installation Installation
   * @param data {@link CreateCustomObjectRecord} Create custom object record data
   * @returns Created custom object record {@link CustomObjectRecord}
   */
  async createCustomObjectRecord(
    installation: Installations,
    data: CreateCustomObjectRecord,
  ): Promise<CustomObjectRecord> {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const createCustomObjectRecordResponse = await this.proxy(
      installation.organization,
      'POST',
      `/v1/custom-object/${data.customObjectId}/records`,
      data,
    );

    if (!createCustomObjectRecordResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to create custom object record!`);
      const createCustomObjectRecordJson =
        await createCustomObjectRecordResponse.json();
      throw new Error(createCustomObjectRecordJson.message);
    }

    // Parse the response
    const customObjectRecord = await createCustomObjectRecordResponse.json();
    return customObjectRecord;
  }

  /**
   * Get custom object records by IDs
   * @param installation Installation
   * @param ids IDs
   * @returns Custom object records {@link CustomObjectRecord[]}
   */
  async getCustomObjectRecordsByIds(
    installation: Installations,
    customObjectId: string,
    ids: string[],
  ): Promise<CustomObjectRecord[]> {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const getCustomObjectRecordsByIdsResponse = await this.proxy(
      installation.organization,
      'POST',
      `/v1/custom-object/${customObjectId}/records/fetchByIds`,
      { ids },
    );

    if (!getCustomObjectRecordsByIdsResponse.ok) {
      this.logger.error(
        `${LOG_SPAN} Failed to get custom object records by IDs!`,
      );
      const getCustomObjectRecordsByIdsJson =
        await getCustomObjectRecordsByIdsResponse.json();
      throw new Error(getCustomObjectRecordsByIdsJson.message);
    }

    // Parse the response
    const customObjectRecords =
      await getCustomObjectRecordsByIdsResponse.json();
    return customObjectRecords.data;
  }

  async updateCustomObjectRecord(
    installation: Installations,
    data: UpdateCustomObjectRecord,
  ): Promise<CustomObjectRecord> {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    const { customObjectId, customObjectRecordId, customFieldValues } = data;

    // Proxy the request
    const updateCustomObjectRecordResponse = await this.proxy(
      installation.organization,
      'PATCH',
      `/v1/custom-object/${customObjectId}/records/${customObjectRecordId}`,
      { customFieldValues },
    );

    if (!updateCustomObjectRecordResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to update custom object record!`);
      const updateCustomObjectRecordJson =
        await updateCustomObjectRecordResponse.json();
      throw new Error(updateCustomObjectRecordJson.message);
    }

    // Parse the response
    const customObjectRecord = await updateCustomObjectRecordResponse.json();
    return customObjectRecord.data;
  }

  /**
   * Delete a custom object record
   * @param installation Installation
   * @param id ID
   * @returns Deleted custom object record {@link CustomObjectRecord}
   */
  async deleteCustomObjectRecord(
    installation: Installations,
    customObjectId: string,
    customObjectRecordId: string,
  ): Promise<CustomObjectRecord> {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const deleteCustomObjectRecordResponse = await this.proxy(
      installation.organization,
      'DELETE',
      `/v1/custom-object/${customObjectId}/records/${customObjectRecordId}`,
    );

    if (!deleteCustomObjectRecordResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to delete custom object record!`);
      const deleteCustomObjectRecordJson =
        await deleteCustomObjectRecordResponse.json();
      throw new Error(deleteCustomObjectRecordJson.message);
    }

    // Parse the response
    const customObjectRecord = await deleteCustomObjectRecordResponse.json();
    return customObjectRecord.data;
  }

  /**
   * Ingest customer contacts
   * @param installation Installation
   * @param data {@link IngestCustomerContactDTO} Ingest customer contact data
   * @returns Ingested customer contacts {@link PlatformCustomerContact[]}
   */
  async ingestCustomerContacts(
    installation: Installations,
    data: IngestCustomerContactDTO,
  ): Promise<PlatformCustomerContact[]> {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const ingestCustomerContactsResponse = await this.proxy(
      installation.organization,
      'POST',
      '/v1/accounts/contacts/ingest',
      data,
    );

    if (!ingestCustomerContactsResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to ingest customer contacts!`);
      const ingestCustomerContactsJson =
        await ingestCustomerContactsResponse.json();
      throw new Error(ingestCustomerContactsJson.message);
    }

    // Parse the response
    const ingestedCustomerContacts =
      await ingestCustomerContactsResponse.json();
    return ingestedCustomerContacts.data;
  }

  /**
   * Create customer contact
   * @param installation Installation
   * @param data {@link CreateCustomerContactDTO} Create customer contact data
   * @returns Created customer contact {@link PlatformCustomerContact}
   */
  async createCustomerContact(
    installation: Installations,
    data: CreateCustomerContactDTO,
  ): Promise<PlatformCustomerContact> {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const createCustomerContactResponse = await this.proxy(
      installation.organization,
      'POST',
      '/v1/accounts/contacts',
      data,
    );

    if (!createCustomerContactResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to create customer contact!`);
      const createCustomerContactJson =
        await createCustomerContactResponse.json();
      throw new Error(createCustomerContactJson.message);
    }

    // Parse the response
    const customerContact = await createCustomerContactResponse.json();
    return customerContact.data;
  }

  /**
   * Get customer contacts by IDs
   * @param installation Installation
   * @param ids IDs
   * @returns Customer contacts {@link PlatformCustomerContact[]}
   */
  async getCustomerContactsByIds(
    installation: Installations,
    ids: string[],
  ): Promise<PlatformCustomerContact[]> {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const getCustomerContactsByIdsResponse = await this.proxy(
      installation.organization,
      'POST',
      '/v1/accounts/contacts/filter/ids',
      { ids },
    );

    if (!getCustomerContactsByIdsResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to get customer contacts by IDs!`);
      const getCustomerContactsByIdsJson =
        await getCustomerContactsByIdsResponse.json();
      throw new Error(getCustomerContactsByIdsJson.message);
    }

    // Parse the response
    const customerContacts = await getCustomerContactsByIdsResponse.json();
    return customerContacts.data;
  }

  /**
   * Update customer contact
   * @param installation Installation
   * @param data {@link UpdateCustomerContactDTO} Update customer contact data
   * @returns Updated customer contact {@link PlatformCustomerContact}
   */
  async updateCustomerContact(
    installation: Installations,
    contactId: string,
    data: UpdateCustomerContactDTO,
  ): Promise<PlatformCustomerContact> {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const updateCustomerContactResponse = await this.proxy(
      installation.organization,
      'PUT',
      `/v1/accounts/contacts/${contactId}`,
      data,
    );

    if (!updateCustomerContactResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to update customer contact!`);
      const updateCustomerContactJson =
        await updateCustomerContactResponse.json();
      throw new Error(updateCustomerContactJson.message);
    }

    // Parse the response
    const customerContact = await updateCustomerContactResponse.json();
    return customerContact.data;
  }

  /**
   * Add a reaction to a comment
   * @param installation Installation
   * @param commentId Comment ID
   * @param reactionName Reaction name
   * @returns Added reaction
   */
  async addReaction(
    installation: Installations,
    commentId: string,
    reactionName: string,
    impersonatedUserName: string,
    impersonatedUserEmail: string,
    impersonatedUserAvatar: string,
  ) {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Proxy the request
    const addReactionResponse = await this.proxy(
      installation.organization,
      'POST',
      `/v1/reactions/${commentId}`,
      {
        name: reactionName,
        impersonatedUserName,
        impersonatedUserEmail,
        impersonatedUserAvatar,
        metadata: { ignoreSelf: true },
      },
    );

    // If the request failed, throw an error
    if (!addReactionResponse.ok) {
      this.logger.error(`${LOG_SPAN} Failed to add reaction to comment!`);
      const addReactionJson = await addReactionResponse.json();
      throw new Error(addReactionJson.message);
    }

    // Parse the response
    const reaction = await addReactionResponse.json();
    return reaction.data;
  }

  /**
   * Remove a reaction from a comment
   * @param installation Installation
   * @param commentId Comment ID
   * @param reactionName Reaction name
   * @returns Removed reaction response
   */
  async removeReaction(
    installation: Installations,
    commentId: string,
    reactionName: string,
    impersonatedUserEmail: string,
  ) {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      this.logger.error(
        `[DEBUG-PLATFORM-API] No API key found for organization ${installation?.organization?.uid}`,
      );
      throw new Error('Organization has no API key');
    }

    const apiUrl = `/v1/reactions/remove/${commentId}/${reactionName}?impersonatedUserEmail=${encodeURIComponent(impersonatedUserEmail)}`;

    try {
      // Proxy the request
      const removeReactionResponse = await this.proxy(
        installation.organization,
        'DELETE',
        apiUrl,
      );

      // Log the response status
      console.log(
        `[DEBUG-PLATFORM-API] Remove reaction response status: ${removeReactionResponse.status}`,
      );

      // If the request failed, throw an error
      if (!removeReactionResponse.ok) {
        const removeReactionJson = await removeReactionResponse.json();
        this.logger.error(
          `[DEBUG-PLATFORM-API] Failed to remove reaction: ${JSON.stringify(removeReactionJson)}`,
        );
        this.logger.error(
          `${LOG_SPAN} Failed to remove reaction from comment!`,
        );
        throw new Error(
          removeReactionJson.message || 'Failed to remove reaction',
        );
      }

      // Parse the response
      const reaction = await removeReactionResponse.json();
      return reaction;
    } catch (error) {
      this.logger.error(
        `[DEBUG-PLATFORM-API] Error removing reaction: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }
}
