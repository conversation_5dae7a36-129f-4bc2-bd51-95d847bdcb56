import { HttpStatus } from '@nestjs/common';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { ConfigKeys, ConfigService } from '../../../../src/config/config.service';
import { Installations } from '../../../../src/database/entities';
import { Organizations } from '../../../../src/database/entities/organizations/organizations.entity';
import { ThenaPlatformApiProvider } from '../../../../src/external/provider/thena-platform-api.provider';
import { UserPlatformLookupService } from '../../../../src/shared/user-platform-lookup/user-platform-lookup.service';
import { ILogger } from '../../../../src/utils';

// Mock global fetch
global.fetch = vi.fn();
global.AbortSignal = {
  timeout: vi.fn().mockReturnValue({}),
} as any;

describe('ThenaPlatformApiProvider', () => {
  let provider: ThenaPlatformApiProvider;
  let mockLogger: ILogger;
  let mockConfigService: ConfigService;
  let mockUserPlatformLookupService: UserPlatformLookupService;
  let mockInstallation: Installations;
  let mockOrganization: Organizations;

  beforeEach(() => {
    vi.resetAllMocks();

    // Mock the logger
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    // Mock the config service
    mockConfigService = {
      get: vi.fn(),
    } as unknown as ConfigService;

    // Mock the user platform lookup service
    mockUserPlatformLookupService = {
      getPlatformUserId: vi.fn(),
      getPlatformUserInfo: vi.fn(),
      hasValidPlatformUserId: vi.fn(),
      refreshUserMapping: vi.fn(),
      clearCache: vi.fn(),
      getCacheStats: vi.fn(),
    } as unknown as UserPlatformLookupService;

    // Mock the organization
    mockOrganization = {
      id: 'org-1',
      uid: 'org-uid-1',
      apiKey: 'api-key-1',
      name: 'Test Organization',
      externalPk: 'ext-1',
      installingUserId: 'user-1',
      metadata: {},
      bots: [],
      users: [],
      channels: [],
      installations: [],
      teams: [],
      subgroups: [],
    };

    // Mock the installation
    mockInstallation = {
      id: 'inst-1',
      teamId: 'team-1',
      teamName: 'Team 1',
      botToken: 'xoxb-token-1',
      organization: mockOrganization,
    } as unknown as Installations;

    // Configure the config service mock
    (mockConfigService.get as Mock).mockImplementation((key: string) => {
      if (key === ConfigKeys.PLATFORM_API_URL) {
        return 'https://api.platform.test';
      }
      if (key === ConfigKeys.ANNOTATOR_API_URL) {
        return 'https://api.annotator.test';
      }
      return '';
    });

    // Create the provider
    provider = new ThenaPlatformApiProvider(mockLogger, mockConfigService, mockUserPlatformLookupService);
  });

  describe('proxy', () => {
    it('should throw an error if the organization has no API key', async () => {
      // Setup
      const orgWithoutApiKey = { ...mockOrganization, apiKey: null };

      // Execute and verify
      try {
        await provider.proxy(orgWithoutApiKey as Organizations, 'GET', '/test');
        // If we reach here, the test should fail because no error was thrown
        expect(true).toBe(false); // This line should not be reached
      } catch (error) {
        // Verify the error message
        expect(error.message).toBe('Organization has no API key');
      }
    });

    it('should make a GET request with the correct headers', async () => {
      // Setup
      const mockResponse = new Response(JSON.stringify({ data: 'test' }), {
        status: 200,
      });
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      await provider.proxy(mockOrganization, 'GET', '/test');

      // Verify
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/test',
        expect.objectContaining({
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'x-org-id': mockOrganization.uid,
            'x-api-key': mockOrganization.apiKey,
            'x-request-source': 'slack',
          },
        })
      );
    });

    it('should make a POST request with the correct body', async () => {
      // Setup
      const mockResponse = new Response(JSON.stringify({ data: 'test' }), {
        status: 200,
      });
      (fetch as Mock).mockResolvedValue(mockResponse);
      const body = { test: 'data' };

      // Execute
      await provider.proxy(mockOrganization, 'POST', '/test', body);

      // Verify
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/test',
        expect.objectContaining({
          method: 'POST',
          headers: expect.any(Object),
          body: JSON.stringify(body),
        })
      );
    });

    it('should handle network errors', async () => {
      // Setup
      (fetch as Mock).mockRejectedValue(new Error('Network error'));

      // Execute and verify
      await expect(
        provider.proxy(mockOrganization, 'GET', '/test')
      ).rejects.toThrow('Network error');
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('createNewTicket', () => {
    it('should create a new ticket successfully', async () => {
      // Setup
      const mockTicketData = {
        requestorEmail: '<EMAIL>',
        title: 'Test Ticket',
        description: 'Test Description',
        teamId: 'team-1',
        urgency: 'high',
        metadata: {
          slack: {
            channel: 'C12345',
            ts: '**********.123456',
            user: 'U12345',
          },
        },
      };

      const mockResponse = new Response(
        JSON.stringify({
          data: {
            id: 'ticket-1',
            title: mockTicketData.title,
          },
        }),
        { status: 200 }
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.createNewTicket(mockInstallation, mockTicketData);

      // Verify
      expect(result).toEqual({
        id: 'ticket-1',
        title: mockTicketData.title,
      });
      expect(fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining(mockTicketData.title),
        })
      );
    });

    it('should throw an error if the ticket creation fails', async () => {
      // Setup
      const mockTicketData = {
        requestorEmail: '<EMAIL>',
        title: 'Test Ticket',
        description: 'Test Description',
        teamId: 'team-1',
        urgency: 'high',
        metadata: {
          slack: {
            channel: 'C12345',
            ts: '**********.123456',
            user: 'U12345',
          },
        },
      };

      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to create ticket',
        }),
        { status: 400 }
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.createNewTicket(mockInstallation, mockTicketData)
      ).rejects.toThrow('Failed to create ticket');
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('getTeams', () => {
    it('should fetch teams successfully', async () => {
      // Setup
      const mockTeams = [
        { id: 'team-1', name: 'Team 1' },
        { id: 'team-2', name: 'Team 2' },
      ];

      const mockResponse = new Response(
        JSON.stringify({
          data: mockTeams,
        }),
        { status: 200 }
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.getTeams(mockInstallation);

      // Verify
      expect(result).toEqual(mockTeams);
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/v1/teams',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'x-org-id': mockOrganization.uid,
            'x-api-key': mockOrganization.apiKey,
          }),
        })
      );
    });

    it('should handle API errors when fetching teams', async () => {
      // Setup
      const mockResponse = new Response('Error fetching teams', {
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      });
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(provider.getTeams(mockInstallation)).rejects.toThrow(
        'Teams API returned status 500'
      );
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it('should handle unexpected response format', async () => {
      // Setup
      const mockResponse = new Response(
        JSON.stringify({
          // Missing data property
          status: 'success',
        }),
        { status: 200 }
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(provider.getTeams(mockInstallation)).rejects.toThrow(
        'Teams API returned unexpected data format'
      );
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('updateTicket', () => {
    it('should update a ticket successfully', async () => {
      // Setup
      const ticketId = 'ticket-1';
      const updateData = {
        title: 'Updated Title',
        statusId: 'status-1',
      };

      const mockResponse = new Response(
        JSON.stringify({
          data: {
            id: ticketId,
            title: updateData.title,
          },
        }),
        { status: 200 }
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.updateTicket(
        mockInstallation,
        ticketId,
        updateData
      );

      // Verify
      expect(result).toEqual({
        data: {
          id: ticketId,
          title: updateData.title,
        },
      });
      expect(fetch).toHaveBeenCalledWith(
        `https://api.platform.test/v1/tickets/${ticketId}`,
        expect.objectContaining({
          method: 'PATCH',
          body: JSON.stringify(updateData),
        })
      );
    });

    it('should throw an error if the ticket update fails', async () => {
      // Setup
      const ticketId = 'ticket-1';
      const updateData = {
        title: 'Updated Title',
      };

      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to update ticket',
        }),
        { status: 400 }
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.updateTicket(mockInstallation, ticketId, updateData)
      ).rejects.toThrow('Failed to update ticket');
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('createNewComment', () => {
    it('should create a comment with X-User-ID header when platform user ID is available', async () => {
      // Setup
      const slackUserId = 'U12345';
      const platformUserId = 'platform-user-123';
      const commentData = {
        content: 'Test comment',
        ticketId: 'ticket-1',
        channelId: 'C12345',
        files: [],
        metadata: {
          ts: '**********.123456',
          ignoreSelf: true,
        },
        slackUserId,
      };

      // Mock the user platform lookup service
      (mockUserPlatformLookupService.getPlatformUserId as Mock).mockResolvedValue(platformUserId);

      const mockResponse = new Response(
        JSON.stringify({
          data: { id: 'comment-1' },
        }),
        { status: 200 }
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      await provider.createNewComment(mockInstallation, commentData);

      // Verify that the X-User-ID header was included
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/v1/tickets/ticket-1/comment',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'X-User-ID': platformUserId,
          }),
        })
      );

      // Verify that the user platform lookup service was called
      expect(mockUserPlatformLookupService.getPlatformUserId).toHaveBeenCalledWith(
        slackUserId,
        mockInstallation
      );
    });

    it('should create a comment without X-User-ID header when platform user ID is not available', async () => {
      // Setup
      const slackUserId = 'U12345';
      const commentData = {
        content: 'Test comment',
        ticketId: 'ticket-1',
        channelId: 'C12345',
        files: [],
        metadata: {
          ts: '**********.123456',
          ignoreSelf: true,
        },
        slackUserId,
      };

      // Mock the user platform lookup service to return null
      (mockUserPlatformLookupService.getPlatformUserId as Mock).mockResolvedValue(null);

      const mockResponse = new Response(
        JSON.stringify({
          data: { id: 'comment-1' },
        }),
        { status: 200 }
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      await provider.createNewComment(mockInstallation, commentData);

      // Verify that the X-User-ID header was NOT included
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/v1/tickets/ticket-1/comment',
        expect.objectContaining({
          method: 'POST',
          headers: expect.not.objectContaining({
            'X-User-ID': expect.anything(),
          }),
        })
      );
    });

    it('should create a comment without X-User-ID header when slackUserId is not provided', async () => {
      // Setup
      const commentData = {
        content: 'Test comment',
        ticketId: 'ticket-1',
        channelId: 'C12345',
        files: [],
        metadata: {
          ts: '**********.123456',
          ignoreSelf: true,
        },
        // No slackUserId provided
      };

      const mockResponse = new Response(
        JSON.stringify({
          data: { id: 'comment-1' },
        }),
        { status: 200 }
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      await provider.createNewComment(mockInstallation, commentData);

      // Verify that the user platform lookup service was NOT called
      expect(mockUserPlatformLookupService.getPlatformUserId).not.toHaveBeenCalled();

      // Verify that the X-User-ID header was NOT included
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/v1/tickets/ticket-1/comment',
        expect.objectContaining({
          method: 'POST',
          headers: expect.not.objectContaining({
            'X-User-ID': expect.anything(),
          }),
        })
      );
    });
  });
});
